<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import AppLayout from '../components/layout/AppLayout.vue'
import DataCard from '../components/common/DataCard.vue'
import LineChart from '../components/charts/LineChart.vue'
import { financeAPI } from '../utils/api'
import { formatCurrency, formatPercent, formatDate } from '../utils/format'

// 加载状态
const loading = ref(true)

// 财务仪表盘数据
const financeData = reactive({
  summary: {
    fxAssetValue: 0,
    fxAssetChange: '',
    fxAssetIncreased: true,

    forwardValue: 0,
    forwardChange: '',
    forwardIncreased: true,

    realizedValue: 0,
    realizedChange: '',
    realizedIncreased: true,

    cashFlowValue: 0,
    cashFlowChange: '',
    cashFlowIncreased: false
  },
  exchangeGain: {
    labels: [],
    values: [],
    period: 'month'
  },
  exchangeRate: {
    currencyPair: '',
    dates: [],
    rates: []
  },
  currencyAssets: [],
  forwardContracts: [],
  realizedGainDetails: [],
  cashFlowDetail: null,
  dataInput: null
})

// 汇兑损益分布图表数据
const exchangeGainChartData = ref({
  labels: [],
  datasets: [
    {
      label: '汇兑损益',
      backgroundColor: (context) => {
        const value = context.dataset.data[context.dataIndex]
        return value >= 0 ? 'rgba(0, 180, 42, 0.7)' : 'rgba(245, 63, 63, 0.7)'
      },
      data: []
    }
  ]
})

// 汇率趋势图表数据
const exchangeRateChartData = ref({
  labels: [],
  datasets: [
    {
      label: '汇率',
      borderColor: '#165DFF',
      backgroundColor: 'rgba(22, 93, 255, 0.1)',
      data: [],
      fill: true
    }
  ]
})

// 期间选择
const period = ref('month')
const periods = [
  { label: '本月', value: 'month' },
  { label: '季度', value: 'quarter' },
  { label: '年度', value: 'year' }
]

// 货币对选择
const currencyPair = ref('USD/CNY')
const currencyPairs = [
  { label: 'USD/CNY', value: 'USD/CNY' },
  { label: 'EUR/CNY', value: 'EUR/CNY' },
  { label: 'JPY/CNY', value: 'JPY/CNY' },
  { label: 'GBP/CNY', value: 'GBP/CNY' }
]

// 远期合约筛选
const contractFilter = ref('all')

// 筛选后的远期合约
const filteredContracts = computed(() => {
  if (contractFilter.value === 'all') {
    return financeData.forwardContracts
  }
  return financeData.forwardContracts.filter(contract => contract.contractType === contractFilter.value)
})

// 为远期合约表格行设置样式
const contractRowClassName = ({ row }) => {
  if (row.isGain) {
    return 'contract-row-gain'
  } else {
    return 'contract-row-loss'
  }
}

// 获取财务仪表盘数据
const fetchFinanceData = async () => {
  loading.value = true
  try {
    const data = await financeAPI.getFinanceDashboard()
    Object.assign(financeData, data)

    // 更新图表数据
    updateExchangeGainChart()
    updateExchangeRateChart()

    loading.value = false
  } catch (error) {
    console.error('获取财务仪表盘数据失败:', error)
    loading.value = false
  }
}

// 获取汇兑损益分布数据
const fetchExchangeGainData = async (periodValue) => {
  try {
    const data = await financeAPI.getExchangeGain({ period: periodValue })
    financeData.exchangeGain = data
    updateExchangeGainChart()
  } catch (error) {
    console.error('获取汇兑损益分布数据失败:', error)
  }
}

// 获取汇率趋势数据
const fetchExchangeRateData = async (pairValue) => {
  try {
    const data = await financeAPI.getExchangeRate({ currencyPair: pairValue })
    financeData.exchangeRate = data
    updateExchangeRateChart()
  } catch (error) {
    console.error('获取汇率趋势数据失败:', error)
  }
}

// 更新汇兑损益图表
const updateExchangeGainChart = () => {
  exchangeGainChartData.value = {
    labels: financeData.exchangeGain.labels,
    datasets: [
      {
        label: '汇兑损益',
        backgroundColor: (context) => {
          const value = financeData.exchangeGain.values[context.dataIndex]
          return value >= 0 ? 'rgba(0, 180, 42, 0.7)' : 'rgba(245, 63, 63, 0.7)'
        },
        data: financeData.exchangeGain.values
      }
    ]
  }
}

// 更新汇率趋势图表
const updateExchangeRateChart = () => {
  exchangeRateChartData.value = {
    labels: financeData.exchangeRate.dates,
    datasets: [
      {
        label: financeData.exchangeRate.currencyPair,
        borderColor: '#165DFF',
        backgroundColor: 'rgba(22, 93, 255, 0.1)',
        data: financeData.exchangeRate.rates,
        fill: true
      }
    ]
  }
}

// 期间切换
const handlePeriodChange = (value) => {
  period.value = value
  fetchExchangeGainData(value)
}

// 货币对切换
const handleCurrencyPairChange = (value) => {
  currencyPair.value = value
  fetchExchangeRateData(value)
}

// 获取已实现损益总额
const getTotalGainLoss = () => {
  if (!financeData.realizedGainDetails || financeData.realizedGainDetails.length === 0) {
    return 0
  }
  return financeData.realizedGainDetails.reduce((sum, item) => sum + item.gainLoss, 0)
}

// 获取汇率变化百分比
const getRateChangePercent = (row) => {
  return Math.abs((row.settlementRate - row.initialRate) / row.initialRate * 100)
}

// 为已实现损益表格行设置样式
const realizedGainRowClass = ({ row }) => {
  if (row.isGain) {
    return 'realized-gain-row-positive'
  } else {
    return 'realized-gain-row-negative'
  }
}

// 获取现金流入总额
const getTotalInflowAmount = () => {
  if (!financeData.cashFlowDetail || !financeData.cashFlowDetail.inflows) {
    return 0
  }
  return new Intl.NumberFormat('zh-CN').format(
    financeData.cashFlowDetail.inflows.reduce((sum, item) => sum + item.amount, 0)
  )
}

// 获取现金流出总额
const getTotalOutflowAmount = () => {
  if (!financeData.cashFlowDetail || !financeData.cashFlowDetail.outflows) {
    return 0
  }
  return new Intl.NumberFormat('zh-CN').format(
    financeData.cashFlowDetail.outflows.reduce((sum, item) => sum + item.amount, 0)
  )
}

// 数据输入面板相关
const activeSection = ref('fx-asset')
const contractType = ref('买入')

// 设置当前活动的输入区域
const setActiveSection = (section) => {
  activeSection.value = section
}

// 计算外币资产预计汇兑损益
const getFxAssetGainPreview = () => {
  const input = financeData.dataInput.fxAssetInput
  if (!input.balance || !input.initialRate || !input.finalRate) {
    return 0
  }
  const initialAmount = input.balance * input.initialRate
  const finalAmount = input.balance * input.finalRate
  return finalAmount - initialAmount
}

// 计算远期合约预计估值损益
const getForwardGainPreview = () => {
  const input = financeData.dataInput.forwardInput
  if (!input.amount || !input.contractRate || !input.currentForwardRate) {
    return 0
  }

  // 买入远期：锁定将来买入外币的汇率，当前汇率高于合约汇率为有利
  // 卖出远期：锁定将来卖出外币的汇率，当前汇率低于合约汇率为有利
  if (contractType.value === '买入') {
    return input.amount * (input.currentForwardRate - input.contractRate)
  } else {
    return input.amount * (input.contractRate - input.currentForwardRate)
  }
}

// 计算已实现损益预计结算损益
const getRealizedGainPreview = () => {
  const input = financeData.dataInput.realizedInput
  if (!input.settlementAmount || !input.initialRate || !input.settlementRate) {
    return 0
  }
  return input.settlementAmount * (input.settlementRate - input.initialRate)
}

// 计算现金流量等值人民币
const getCashFlowCnyPreview = () => {
  const input = financeData.dataInput.cashFlowInput
  if (!input.amount || !input.exchangeRate) {
    return 0
  }
  return input.amount * input.exchangeRate
}

// 获取已实现损益详情
const fetchRealizedGainDetails = async () => {
  try {
    const data = await financeAPI.getRealizedGainDetails()
    financeData.realizedGainDetails = data
  } catch (error) {
    console.error('获取已实现损益详情失败:', error)
  }
}

// 获取现金流量详情
const fetchCashFlowDetail = async () => {
  try {
    const data = await financeAPI.getCashFlowDetail()
    financeData.cashFlowDetail = data
  } catch (error) {
    console.error('获取现金流量详情失败:', error)
  }
}

// 获取数据输入面板配置
const fetchDataInputConfig = async () => {
  try {
    const data = await financeAPI.getDataInputConfig()
    financeData.dataInput = data
  } catch (error) {
    console.error('获取数据输入面板配置失败:', error)
  }
}

// 重新计算财务数据
const handleRecalculate = async () => {
  if (!financeData.dataInput) return

  loading.value = true
  try {
    const data = await financeAPI.recalculateFinanceData(financeData.dataInput)
    Object.assign(financeData, data)

    // 更新图表数据
    updateExchangeGainChart()
    updateExchangeRateChart()

    loading.value = false
  } catch (error) {
    console.error('重新计算财务数据失败:', error)
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchFinanceData()
  fetchRealizedGainDetails()
  fetchCashFlowDetail()
  fetchDataInputConfig()
})
</script>

<template>
  <AppLayout>
    <div class="finance-dashboard">
      <!-- 概览卡片 -->
      <div class="summary-cards">
        <!-- 外币资产卡片 -->
        <DataCard title="外币资产" :loading="loading">
          <div class="summary-card">
            <div class="card-icon fx-asset">
              <i class="fas fa-money-bill"></i>
            </div>
            <div class="card-content">
              <div class="card-value">
                <span class="currency">¥</span>
                {{ new Intl.NumberFormat('zh-CN').format(financeData.summary.fxAssetValue) }}
              </div>
              <div
                class="card-change"
                :class="{ 'increased': financeData.summary.fxAssetIncreased, 'decreased': !financeData.summary.fxAssetIncreased }"
              >
                <i :class="financeData.summary.fxAssetIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ financeData.summary.fxAssetChange }}
              </div>
            </div>
          </div>
        </DataCard>

        <!-- 远期合约卡片 -->
        <DataCard title="远期合约" :loading="loading">
          <div class="summary-card">
            <div class="card-icon forward">
              <i class="fas fa-calendar-check"></i>
            </div>
            <div class="card-content">
              <div class="card-value">
                <span class="currency">¥</span>
                {{ new Intl.NumberFormat('zh-CN').format(financeData.summary.forwardValue) }}
              </div>
              <div
                class="card-change"
                :class="{ 'increased': financeData.summary.forwardIncreased, 'decreased': !financeData.summary.forwardIncreased }"
              >
                <i :class="financeData.summary.forwardIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ financeData.summary.forwardChange }}
              </div>
            </div>
          </div>
        </DataCard>

        <!-- 已实现损益卡片 -->
        <DataCard title="已实现损益" :loading="loading">
          <div class="summary-card">
            <div class="card-icon realized">
              <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="card-content">
              <div class="card-value">
                <span class="currency">¥</span>
                {{ new Intl.NumberFormat('zh-CN').format(financeData.summary.realizedValue) }}
              </div>
              <div
                class="card-change"
                :class="{ 'increased': financeData.summary.realizedIncreased, 'decreased': !financeData.summary.realizedIncreased }"
              >
                <i :class="financeData.summary.realizedIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ financeData.summary.realizedChange }}
              </div>
            </div>
          </div>
        </DataCard>

        <!-- 现金流量卡片 -->
        <DataCard title="现金流量影响" :loading="loading">
          <div class="summary-card">
            <div class="card-icon cash-flow">
              <i class="fas fa-cash-register"></i>
            </div>
            <div class="card-content">
              <div class="card-value">
                <span class="currency">¥</span>
                {{ new Intl.NumberFormat('zh-CN').format(financeData.summary.cashFlowValue) }}
              </div>
              <div
                class="card-change"
                :class="{ 'increased': financeData.summary.cashFlowIncreased, 'decreased': !financeData.summary.cashFlowIncreased }"
              >
                <i :class="financeData.summary.cashFlowIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ financeData.summary.cashFlowChange }}
              </div>
            </div>
          </div>
        </DataCard>
      </div>

      <!-- 图表区域 -->
      <div class="chart-section">
        <!-- 汇兑损益分布图表 -->
        <DataCard title="汇兑损益分布" :loading="loading" class="gain-chart">
          <div class="chart-header">
            <div class="chart-period">
              <el-radio-group v-model="period" @change="handlePeriodChange">
                <el-radio-button
                  v-for="item in periods"
                  :key="item.value"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <LineChart :chart-data="exchangeGainChartData" :height="300" />
        </DataCard>

        <!-- 汇率趋势图表 -->
        <DataCard title="汇率趋势" :loading="loading" class="rate-chart">
          <div class="chart-header">
            <el-select v-model="currencyPair" @change="handleCurrencyPairChange" size="small">
              <el-option
                v-for="item in currencyPairs"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <LineChart :chart-data="exchangeRateChartData" :height="300" />
        </DataCard>
      </div>

      <!-- 详细数据区域 -->
      <div class="detail-section detail-section-spaced">
        <!-- 外币资产详情 -->
        <DataCard title="外币资产详情" id="fx-asset" :loading="loading">
          <div class="fx-asset-container">
            <div class="asset-cards">
              <div
                v-for="(item, index) in financeData.currencyAssets"
                :key="index"
                class="asset-card"
                :class="{ 'gain-border': item.isGain, 'loss-border': !item.isGain }"
              >
                <div class="asset-card-header">
                  <div class="asset-title">
                    <span class="asset-name">{{ item.name }}</span>
                    <span class="asset-amount-display">{{ new Intl.NumberFormat('zh-CN').format(item.amount) }} {{ item.currency }}</span>
                  </div>
                </div>

                <div class="asset-rates">
                  <div class="rate-item">
                    <span class="rate-label">期初汇率</span>
                    <span class="rate-value">{{ item.initialRate }}</span>
                  </div>
                  <div class="rate-item">
                    <span class="rate-label">期末汇率</span>
                    <span class="rate-value">{{ item.finalRate }}</span>
                  </div>
                </div>

                <div class="asset-result">
                  <span class="result-label">汇兑损益</span>
                  <span
                    class="result-value"
                    :class="{ 'gain-text': item.isGain, 'loss-text': !item.isGain }"
                  >
                    {{ item.isGain ? '+' : '' }}{{ new Intl.NumberFormat('zh-CN').format(item.exchangeGain) }} CNY
                  </span>
                </div>
              </div>
            </div>

            <button class="view-more-btn">
              查看更多
            </button>
          </div>
        </DataCard>

        <!-- 远期合约数据 -->
        <DataCard title="远期合约详情" id="forward-contract" :loading="loading">
          <div class="forward-contract-container">
            <div class="contract-cards">
              <div
                v-for="(contract, index) in filteredContracts"
                :key="index"
                class="contract-card"
                :class="{ 'gain-border': contract.isGain, 'loss-border': !contract.isGain }"
              >
                <div class="contract-card-header">
                  <div class="contract-title">
                    <span class="contract-name">{{ contract.contractType }}合约</span>
                    <span class="contract-amount">{{ new Intl.NumberFormat('zh-CN').format(contract.amount) }} {{ contract.currency }}</span>
                  </div>
                </div>

                <div class="contract-rates">
                  <div class="rate-item">
                    <span class="rate-label">约定汇率</span>
                    <span class="rate-value">{{ contract.contractRate }}</span>
                  </div>
                  <div class="rate-item">
                    <span class="rate-label">当前远期汇率</span>
                    <span class="rate-value">{{ contract.marketRate }}</span>
                  </div>
                  <div class="rate-item">
                    <span class="rate-label">剩余期限</span>
                    <span class="rate-value">{{ contract.remainingDays }}天</span>
                  </div>
                </div>

                <div class="contract-result">
                  <span class="result-label">公允价值</span>
                  <span
                    class="result-value"
                    :class="{ 'gain-text': contract.isGain, 'loss-text': !contract.isGain }"
                  >
                    {{ contract.isGain ? '+' : '' }}{{ new Intl.NumberFormat('zh-CN').format(contract.valuationGain) }} CNY
                  </span>
                </div>
              </div>
            </div>

            <button class="add-contract-btn">
              新增合约
            </button>
          </div>
        </DataCard>
      </div>

      <!-- 已实现损益和现金流量区域 -->
      <div class="detail-section detail-section-spaced">
        <!-- 已实现损益详情 -->
        <DataCard title="已实现损益详情" id="realized-gain" :loading="loading">
          <div class="realized-gain-table">
            <div class="realized-gain-summary">
              <div class="summary-item">
                <span class="summary-label">总结算金额</span>
                <span class="summary-value">{{
                  new Intl.NumberFormat('zh-CN').format(
                    financeData.realizedGainDetails?.reduce((sum, item) => sum + item.amount, 0) || 0
                  )
                }} USD</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">总损益</span>
                <span class="summary-value" :class="{
                  'increased': getTotalGainLoss() >= 0,
                  'decreased': getTotalGainLoss() < 0
                }">
                  {{ getTotalGainLoss() >= 0 ? '+' : '' }}{{
                    new Intl.NumberFormat('zh-CN').format(getTotalGainLoss())
                  }} CNY
                </span>
              </div>
            </div>
            <el-table
              :data="financeData.realizedGainDetails"
              style="width: 100%"
              :row-class-name="realizedGainRowClass"
              stripe
            >
              <el-table-column prop="transactionDate" label="交易日期" width="110">
                <template #default="scope">
                  <div class="transaction-date">{{ scope.row.transactionDate }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="currency" label="币种" width="70" />
              <el-table-column prop="amount" label="金额" width="120">
                <template #default="scope">
                  <div class="amount-display">
                    <div class="amount-value">{{ new Intl.NumberFormat('zh-CN').format(scope.row.amount) }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="汇率信息" width="180">
                <template #default="scope">
                  <div class="rate-comparison">
                    <div class="rate-item">
                      <span class="rate-label">初始汇率:</span>
                      <span class="rate-value">{{ scope.row.initialRate }}</span>
                    </div>
                    <div class="rate-item">
                      <span class="rate-label">结算汇率:</span>
                      <span class="rate-value">{{ scope.row.settlementRate }}</span>
                    </div>
                    <div class="rate-change" :class="{
                      'increased': scope.row.settlementRate > scope.row.initialRate,
                      'decreased': scope.row.settlementRate < scope.row.initialRate
                    }">
                      <i :class="scope.row.settlementRate > scope.row.initialRate ? 'el-icon-top' : 'el-icon-bottom'"></i>
                      {{ getRateChangePercent(scope.row).toFixed(2) }}%
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="gainLoss" label="损益" min-width="120">
                <template #default="scope">
                  <span
                    class="gain-loss-value"
                    :class="{ 'increased': scope.row.isGain, 'decreased': !scope.row.isGain }"
                  >
                    {{ scope.row.isGain ? '+' : '' }}{{ new Intl.NumberFormat('zh-CN').format(scope.row.gainLoss) }} CNY
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </DataCard>

        <!-- 现金流量详情 -->
        <DataCard title="现金流量汇率影响" id="cash-flow" :loading="loading">
          <div class="cash-flow-detail" v-if="financeData.cashFlowDetail">
            <div class="cash-flow-summary">
              <div class="cash-flow-chart">
                <div class="flow-chart-item">
                  <div class="flow-chart-line flow-in"></div>
                  <div class="flow-chart-amount">流入: {{ getTotalInflowAmount() }} USD</div>
                </div>
                <div class="flow-chart-item">
                  <div class="flow-chart-line flow-out"></div>
                  <div class="flow-chart-amount">流出: {{ getTotalOutflowAmount() }} USD</div>
                </div>
              </div>
            </div>

            <!-- 期初余额 -->
            <div class="cash-flow-item initial-balance">
              <div class="cash-flow-header">
                <div class="cash-flow-icon">
                  <i class="fas fa-money-bill"></i>
                </div>
                <h4 class="cash-flow-title">期初余额</h4>
                <span class="cash-flow-amount">
                  {{ financeData.cashFlowDetail.initialBalance?.amount }} {{ financeData.cashFlowDetail.initialBalance?.currency }}
                </span>
              </div>
              <div class="cash-flow-details">
                <div class="detail-grid">
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">期初汇率</span>
                      <span class="detail-value">{{ financeData.cashFlowDetail.initialBalance?.exchangeRate }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">人民币金额</span>
                      <span class="detail-value">¥ {{ new Intl.NumberFormat('zh-CN').format(financeData.cashFlowDetail.initialBalance?.cnyAmount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 本期流入 -->
            <div class="cash-flow-item inflow">
              <div class="cash-flow-header">
                <div class="cash-flow-icon">
                  <i class="fas fa-arrow-down"></i>
                </div>
                <h4 class="cash-flow-title">本期流入</h4>
                <span class="cash-flow-amount">
                  {{ getTotalInflowAmount() }} USD
                </span>
              </div>
              <div class="cash-flow-details">
                <div v-for="(inflow, index) in financeData.cashFlowDetail.inflows" :key="index" class="detail-grid">
                  <div class="detail-col inflow-desc">
                    <div class="detail-row">
                      <span class="detail-value">{{ inflow.description }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-value">{{ new Intl.NumberFormat('zh-CN').format(inflow.amount) }} {{ inflow.currency }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">@ {{ inflow.exchangeRate }}</span>
                      <span class="detail-value">¥ {{ new Intl.NumberFormat('zh-CN').format(inflow.cnyAmount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 本期流出 -->
            <div class="cash-flow-item outflow">
              <div class="cash-flow-header">
                <div class="cash-flow-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <h4 class="cash-flow-title">本期流出</h4>
                <span class="cash-flow-amount">
                  {{ getTotalOutflowAmount() }} USD
                </span>
              </div>
              <div class="cash-flow-details">
                <div v-for="(outflow, index) in financeData.cashFlowDetail.outflows" :key="index" class="detail-grid">
                  <div class="detail-col outflow-desc">
                    <div class="detail-row">
                      <span class="detail-value">{{ outflow.description }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-value">{{ new Intl.NumberFormat('zh-CN').format(outflow.amount) }} {{ outflow.currency }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">@ {{ outflow.exchangeRate }}</span>
                      <span class="detail-value">¥ {{ new Intl.NumberFormat('zh-CN').format(outflow.cnyAmount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 期末余额 -->
            <div class="cash-flow-item final-balance">
              <div class="cash-flow-header">
                <div class="cash-flow-icon">
                  <i class="fas fa-wallet"></i>
                </div>
                <h4 class="cash-flow-title">期末余额</h4>
                <span class="cash-flow-amount">
                  {{ financeData.cashFlowDetail.finalBalance?.amount }} {{ financeData.cashFlowDetail.finalBalance?.currency }}
                </span>
              </div>
              <div class="cash-flow-details">
                <div class="detail-grid">
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">期末汇率</span>
                      <span class="detail-value">{{ financeData.cashFlowDetail.finalBalance?.exchangeRate }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">人民币金额</span>
                      <span class="detail-value">¥ {{ new Intl.NumberFormat('zh-CN').format(financeData.cashFlowDetail.finalBalance?.cnyAmount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 汇率变动影响 -->
            <div class="cash-flow-impact" v-if="financeData.cashFlowDetail.exchangeRateImpact">
              <div class="impact-header">
                <div class="impact-icon" :class="{ 'negative-impact': financeData.cashFlowDetail.exchangeRateImpact.isNegative }">
                  <i class="fas fa-exchange-alt"></i>
                </div>
                <h4 class="impact-title">汇率变动影响</h4>
                <span
                  class="impact-amount"
                  :class="{ 'decreased': financeData.cashFlowDetail.exchangeRateImpact.isNegative, 'increased': !financeData.cashFlowDetail.exchangeRateImpact.isNegative }"
                >
                  {{ financeData.cashFlowDetail.exchangeRateImpact.isNegative ? '' : '+' }}{{ new Intl.NumberFormat('zh-CN').format(financeData.cashFlowDetail.exchangeRateImpact.impact) }} CNY
                </span>
              </div>
              <p class="impact-description">{{ financeData.cashFlowDetail.exchangeRateImpact.description }}</p>
            </div>
          </div>
        </DataCard>
      </div>

      <!-- 数据输入面板 -->
      <DataCard title="数据输入面板" :loading="loading" v-if="financeData.dataInput" class="data-input-card">
        <div class="data-input-panel">
          <div class="panel-header">
            <div class="panel-title">
              <i class="fas fa-sliders-h"></i>
              <span>外汇风险管理数据维护</span>
            </div>
            <el-button type="primary" @click="handleRecalculate" :loading="loading">
              <i class="fas fa-calculator"></i> 重新计算
            </el-button>
          </div>

          <div class="input-grid">
            <!-- 外币资产输入 -->
            <div class="input-section" :class="{'active-section': activeSection === 'fx-asset'}" @click="setActiveSection('fx-asset')">
              <div class="section-header">
                <div class="section-icon fx-asset">
                  <i class="fas fa-money-bill"></i>
                </div>
                <h4>外币资产录入</h4>
              </div>
              <div class="input-form">
                <div class="form-row">
                  <div class="form-item">
                    <label>币种</label>
                    <el-select v-model="financeData.dataInput.fxAssetInput.currency" size="small" class="full-width">
                      <el-option
                        v-for="currency in financeData.dataInput.currencies"
                        :key="currency"
                        :label="currency"
                        :value="currency"
                      />
                    </el-select>
                  </div>
                  <div class="form-item">
                    <label>外币余额</label>
                    <el-input-number
                      v-model="financeData.dataInput.fxAssetInput.balance"
                      :precision="2"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label>期初汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.fxAssetInput.initialRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                  <div class="form-item">
                    <label>期末汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.fxAssetInput.finalRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-result" v-if="financeData.dataInput.fxAssetInput.balance">
                  <div class="result-item">
                    <span>预计汇兑损益：</span>
                    <span :class="{
                      'increased': getFxAssetGainPreview() >= 0,
                      'decreased': getFxAssetGainPreview() < 0
                    }">
                      {{ getFxAssetGainPreview() >= 0 ? '+' : '' }}{{ getFxAssetGainPreview().toFixed(2) }} CNY
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 远期合约输入 -->
            <div class="input-section" :class="{'active-section': activeSection === 'forward'}" @click="setActiveSection('forward')">
              <div class="section-header">
                <div class="section-icon forward">
                  <i class="fas fa-calendar-check"></i>
                </div>
                <h4>远期合约录入</h4>
              </div>
              <div class="input-form">
                <div class="form-row">
                  <div class="form-item">
                    <label>币种</label>
                    <el-select v-model="financeData.dataInput.forwardInput.currency" size="small" class="full-width">
                      <el-option
                        v-for="currency in financeData.dataInput.currencies"
                        :key="currency"
                        :label="currency"
                        :value="currency"
                      />
                    </el-select>
                  </div>
                  <div class="form-item">
                    <label>类型</label>
                    <el-select v-model="contractType" size="small" class="full-width">
                      <el-option label="买入" value="买入" />
                      <el-option label="卖出" value="卖出" />
                    </el-select>
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label>合约金额</label>
                    <el-input-number
                      v-model="financeData.dataInput.forwardInput.amount"
                      :precision="2"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                  <div class="form-item">
                    <label>约定汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.forwardInput.contractRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label>当前远期汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.forwardInput.currentForwardRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                  <div class="form-item">
                    <label>剩余期限(月)</label>
                    <el-input-number
                      v-model="financeData.dataInput.forwardInput.remainingMonths"
                      :min="1"
                      :max="24"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-result" v-if="financeData.dataInput.forwardInput.amount">
                  <div class="result-item">
                    <span>预计估值损益：</span>
                    <span :class="{
                      'increased': getForwardGainPreview() >= 0,
                      'decreased': getForwardGainPreview() < 0
                    }">
                      {{ getForwardGainPreview() >= 0 ? '+' : '' }}{{ getForwardGainPreview().toFixed(2) }} CNY
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 已实现损益输入 -->
            <div class="input-section" :class="{'active-section': activeSection === 'realized'}" @click="setActiveSection('realized')">
              <div class="section-header">
                <div class="section-icon realized">
                  <i class="fas fa-exchange-alt"></i>
                </div>
                <h4>已实现损益录入</h4>
              </div>
              <div class="input-form">
                <div class="form-row">
                  <div class="form-item">
                    <label>币种</label>
                    <el-select v-model="financeData.dataInput.realizedInput.currency" size="small" class="full-width">
                      <el-option
                        v-for="currency in financeData.dataInput.currencies"
                        :key="currency"
                        :label="currency"
                        :value="currency"
                      />
                    </el-select>
                  </div>
                  <div class="form-item">
                    <label>结算金额</label>
                    <el-input-number
                      v-model="financeData.dataInput.realizedInput.settlementAmount"
                      :precision="2"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label>初始汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.realizedInput.initialRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                  <div class="form-item">
                    <label>结算汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.realizedInput.settlementRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-result" v-if="financeData.dataInput.realizedInput.settlementAmount">
                  <div class="result-item">
                    <span>预计结算损益：</span>
                    <span :class="{
                      'increased': getRealizedGainPreview() >= 0,
                      'decreased': getRealizedGainPreview() < 0
                    }">
                      {{ getRealizedGainPreview() >= 0 ? '+' : '' }}{{ getRealizedGainPreview().toFixed(2) }} CNY
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 现金流量输入 -->
            <div class="input-section" :class="{'active-section': activeSection === 'cash-flow'}" @click="setActiveSection('cash-flow')">
              <div class="section-header">
                <div class="section-icon cash-flow">
                  <i class="fas fa-cash-register"></i>
                </div>
                <h4>现金流量录入</h4>
              </div>
              <div class="input-form">
                <div class="form-row">
                  <div class="form-item">
                    <label>币种</label>
                    <el-select v-model="financeData.dataInput.cashFlowInput.currency" size="small" class="full-width">
                      <el-option
                        v-for="currency in financeData.dataInput.currencies"
                        :key="currency"
                        :label="currency"
                        :value="currency"
                      />
                    </el-select>
                  </div>
                  <div class="form-item">
                    <label>流向类型</label>
                    <el-select v-model="financeData.dataInput.cashFlowInput.flowType" size="small" class="full-width">
                      <el-option label="流入" value="流入" />
                      <el-option label="流出" value="流出" />
                    </el-select>
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label>金额</label>
                    <el-input-number
                      v-model="financeData.dataInput.cashFlowInput.amount"
                      :precision="2"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                  <div class="form-item">
                    <label>汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.cashFlowInput.exchangeRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-result" v-if="financeData.dataInput.cashFlowInput.amount">
                  <div class="result-item">
                    <span>等值人民币：</span>
                    <span>¥ {{ getCashFlowCnyPreview().toFixed(2) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DataCard>
    </div>
  </AppLayout>
</template>

<style scoped>
.finance-dashboard {
  min-height: 100%;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 16px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.card-icon i {
  font-size: 20px;
  color: #fff;
}

.card-icon.fx-asset {
  background-color: rgba(22, 93, 255, 0.1);
}

.card-icon.fx-asset i {
  color: #165DFF;
}

.card-icon.forward {
  background-color: rgba(54, 203, 203, 0.1);
}

.card-icon.forward i {
  color: #36CBCB;
}

.card-icon.realized {
  background-color: rgba(82, 196, 26, 0.1);
}

.card-icon.realized i {
  color: #52C41A;
}

.card-icon.cash-flow {
  background-color: rgba(250, 173, 20, 0.1);
}

.card-icon.cash-flow i {
  color: #FAAD14;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.currency {
  font-size: 14px;
  margin-right: 2px;
}

.card-change {
  font-size: 14px;
  display: flex;
  align-items: center;
}

.card-change i {
  margin-right: 4px;
}

.increased {
  color: #52C41A;
}

.decreased {
  color: #FF4D4F;
}

.chart-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.chart-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.detail-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.detail-section-spaced {
  margin-bottom: 24px;
}

/* 外币资产详情样式 */
.fx-asset-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.asset-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.asset-card {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background-color: #f5f7fa;
  transition: all 0.3s ease;
}

.asset-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.gain-border {
  border-left: 4px solid #52c41a;
}

.loss-border {
  border-left: 4px solid #ff4d4f;
}

.asset-card-header {
  margin-bottom: 12px;
}

.asset-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.asset-name {
  font-weight: 600;
  color: #333;
}

.asset-amount-display {
  font-weight: bold;
  color: #165dff;
}

.asset-rates {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.rate-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rate-label {
  font-size: 13px;
  color: #8c8c8c;
}

.rate-value {
  font-weight: 500;
  color: #333;
}

.asset-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e8e8e8;
}

.result-label {
  font-size: 13px;
  color: #8c8c8c;
}

.result-value {
  font-weight: bold;
  font-size: 16px;
}

.gain-text {
  color: #52c41a;
}

.loss-text {
  color: #ff4d4f;
}

.view-more-btn {
  width: 100%;
  padding: 12px;
  border: 1px solid #165dff;
  background-color: transparent;
  color: #165dff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.view-more-btn:hover {
  background-color: #165dff;
  color: white;
}

/* 远期合约详情样式 */
.forward-contract-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contract-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contract-card {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background-color: #f5f7fa;
  transition: all 0.3s ease;
}

.contract-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.contract-card-header {
  margin-bottom: 12px;
}

.contract-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.contract-name {
  font-weight: 600;
  color: #333;
}

.contract-amount {
  font-weight: bold;
  color: #165dff;
}

.contract-rates {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 12px;
}

.contract-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e8e8e8;
}

.add-contract-btn {
  width: 100%;
  padding: 12px;
  border: 1px solid #165dff;
  background-color: transparent;
  color: #165dff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.add-contract-btn:hover {
  background-color: #165dff;
  color: white;
}

/* 现金流量详情样式 */
.cash-flow-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cash-flow-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 4px;
}

.cash-flow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.cash-flow-title {
  font-weight: 600;
  color: #333;
  margin: 0;
}

.cash-flow-amount {
  font-weight: bold;
  color: #165DFF;
}

.cash-flow-details {
  border-top: 1px solid #eee;
  padding-top: 12px;
}

.cash-flow-impact {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
}

.impact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.impact-title {
  font-weight: 600;
  color: #333;
  margin: 0;
}

.impact-amount {
  font-weight: bold;
}

.impact-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 数据输入面板样式 */
.data-input-panel {
  width: 100%;
}

.data-input-card {
  margin-top: 24px;
}

.panel-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;
}

.input-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.input-section {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  transition: border-color 0.3s ease;
  background-color: #fafafa;
}

.input-section:hover {
  border-color: #165DFF;
  box-shadow: 0 4px 12px rgba(22, 93, 255, 0.1);
  transform: translateY(-2px);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.section-header i {
  color: #165DFF;
  font-size: 16px;
}

.section-header h4 {
  font-weight: 600;
  color: #333;
  margin: 0;
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-item label {
  font-size: 13px;
  font-weight: 500;
  color: #666;
}

@media (max-width: 1200px) {
  .summary-cards {
    grid-template-columns: 1fr 1fr;
  }

  .chart-section,
  .detail-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .detail-section-spaced {
    margin-bottom: 20px;
  }

  .input-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .input-section {
    padding: 16px;
  }
}

/* 外币资产详情样式改进 */
.asset-item {
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  overflow: hidden;
}

.asset-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #ccc;
}

.asset-item-gain {
  border-left: 4px solid #52C41A;
}

.asset-item-loss {
  border-left: 4px solid #FF4D4F;
}

.asset-info {
  flex: 1;
}

.asset-description {
  font-size: 13px;
  color: #666;
  margin-top: 4px;
}

.asset-category {
  margin-top: 8px;
  display: flex;
  gap: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.asset-amount-section {
  text-align: right;
}

.currency-code {
  font-size: 14px;
  color: #666;
  margin-right: 4px;
}

.amount-value {
  font-size: 18px;
  font-weight: bold;
}

.asset-cny {
  font-size: 13px;
  color: #666;
  margin-top: 4px;
}

.exchange-gain {
  font-weight: bold;
  font-size: 16px;
}

.asset-details {
  padding-top: 16px;
}

.detail-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.exchange-gain-col {
  border-left: 1px dashed #eee;
  padding-left: 16px;
}

/* 远期合约表格样式改进 */
.contract-wrapper {
  display: flex;
  flex-direction: column;
}

.contract-filter {
  align-self: flex-end;
  margin-bottom: 16px;
}

.contract-no {
  display: flex;
  align-items: center;
  gap: 8px;
}

.contract-desc {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.text-secondary {
  font-size: 12px;
  color: #666;
}

.rate-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rate-item {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.rate-label {
  color: #666;
  margin-right: 4px;
  white-space: nowrap;
}

.rate-value {
  font-weight: 500;
}

.rate-diff {
  margin-top: 4px;
  font-size: 12px;
  font-weight: bold;
}

.remaining-days {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.valuation-gain {
  font-weight: bold;
}

.contract-row-gain {
  background-color: rgba(82, 196, 26, 0.05);
}

.contract-row-loss {
  background-color: rgba(245, 63, 63, 0.05);
}

/* 已实现损益详情样式改进 */
.realized-gain-table {
  display: flex;
  flex-direction: column;
}

.realized-gain-summary {
  display: flex;
  justify-content: flex-end;
  gap: 32px;
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.summary-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 18px;
  font-weight: bold;
}

.transaction-date {
  font-weight: 500;
}

.amount-display {
  display: flex;
  flex-direction: column;
}

.rate-change {
  font-size: 12px;
  margin-top: 4px;
  font-weight: bold;
}

.gain-loss-value {
  font-weight: bold;
}

.realized-gain-row-positive td {
  background-color: rgba(82, 196, 26, 0.05) !important;
}

.realized-gain-row-negative td {
  background-color: rgba(245, 63, 63, 0.05) !important;
}

/* 现金流量详情样式改进 */
.cash-flow-summary {
  margin-bottom: 16px;
}

.cash-flow-chart {
  display: flex;
  gap: 24px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.flow-chart-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.flow-chart-line {
  height: 8px;
  width: 100%;
  border-radius: 4px;
}

.flow-in {
  background-color: #52C41A;
}

.flow-out {
  background-color: #FF4D4F;
}

.flow-chart-amount {
  font-weight: 500;
}

.cash-flow-item {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.cash-flow-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.initial-balance {
  border-left: 4px solid #165DFF;
}

.inflow {
  border-left: 4px solid #52C41A;
}

.outflow {
  border-left: 4px solid #FF4D4F;
}

.final-balance {
  border-left: 4px solid #FAAD14;
}

.cash-flow-header {
  padding: 12px 16px;
  background-color: #f9f9f9;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  position: relative;
}

.cash-flow-icon {
  width: 32px;
  height: 32px;
  background-color: #fff;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.initial-balance .cash-flow-icon i {
  color: #165DFF;
}

.inflow .cash-flow-icon i {
  color: #52C41A;
}

.outflow .cash-flow-icon i {
  color: #FF4D4F;
}

.final-balance .cash-flow-icon i {
  color: #FAAD14;
}

.cash-flow-details {
  padding: 16px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 16px;
  padding: 8px 0;
  border-bottom: 1px dashed #eee;
}

.detail-grid:last-child {
  border-bottom: none;
}

.inflow-desc, .outflow-desc {
  font-weight: 500;
}

.impact-icon {
  width: 32px;
  height: 32px;
  background-color: #fff;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: #52C41A;
}

.negative-impact {
  color: #FF4D4F;
}

/* 数据输入面板样式改进 */
.data-input-panel {
  width: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-bottom: 1px solid #eee;
  padding-bottom: 16px;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
}

.input-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.input-section {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.active-section {
  border-color: #165DFF;
  box-shadow: 0 4px 12px rgba(22, 93, 255, 0.1);
}

.section-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.section-icon.fx-asset {
  background-color: rgba(22, 93, 255, 0.1);
}

.section-icon.fx-asset i {
  color: #165DFF;
}

.section-icon.forward {
  background-color: rgba(54, 203, 203, 0.1);
}

.section-icon.forward i {
  color: #36CBCB;
}

.section-icon.realized {
  background-color: rgba(82, 196, 26, 0.1);
}

.section-icon.realized i {
  color: #52C41A;
}

.section-icon.cash-flow {
  background-color: rgba(250, 173, 20, 0.1);
}

.section-icon.cash-flow i {
  color: #FAAD14;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  font-weight: 600;
  margin: 0;
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-item label {
  font-size: 13px;
  color: #666;
}

.full-width {
  width: 100%;
}

.form-result {
  padding-top: 16px;
  margin-top: 16px;
  border-top: 1px dashed #eee;
}

.result-item {
  display: flex;
  justify-content: space-between;
  font-weight: 500;
}

@media (max-width: 1200px) {
  .summary-cards {
    grid-template-columns: 1fr 1fr;
  }

  .chart-section,
  .detail-section {
    grid-template-columns: 1fr;
  }

  .input-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (max-width: 768px) {
  .summary-cards {
    grid-template-columns: 1fr;
  }

  .detail-row {
    flex-direction: column;
    gap: 8px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .realized-gain-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .summary-item {
    align-items: flex-start;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .cash-flow-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .impact-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
